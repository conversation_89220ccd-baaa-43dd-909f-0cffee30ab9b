/* ====== GLOBALNI STILOVI ====== */
body {
    font-family: 'Montserrat', sans-serif;
}
p {
    font-family: 'Open Sans', sans-serif;
}

/* ====== NAVIGACIJA ====== */
.navbar {
    background-color: #d9eafc;
}
.navbar-brand {
    font-size: 2rem;
    font-weight: 700;
    color: #222 !important;
    transition: color 0.2s, transform 0.2s;
}
.navbar-brand.active,
.navbar-brand[aria-current="page"] {
    color: #d32f2f !important;
}
.navbar-brand:hover, .navbar-brand:focus {
    color: #0d6efd !important;
    transform: scale(1.12) rotate(-3deg);
    text-decoration: none;
}
.nav-link {
    transition: color 0.2s, transform 0.2s;
    color: #222;
    font-size: 1.1rem;
    margin-left: 10px;
}
.nav-link:hover, .nav-link:focus {
    color: #0d6efd;
    transform: scale(1.12) rotate(-3deg);
    text-decoration: none;
}
.social-icon {
    transition: color 0.2s, transform 0.2s;
    color: #222;
    font-size: 1.7rem;
    margin-left: 18px;
}
.social-icon:hover {
    color: #0d6efd;
    transform: scale(1.2) rotate(-8deg);
    text-decoration: none;
}

/* ====== GLOBALNE KARTICE ====== */
.card,
.price-card,
.service-card,
.contact-card,
.feature-box,
.testimonial-card,
.contact-info-card {
    border-radius: 10px;
    background: #fff;
    box-shadow: 0 4px 8px rgba(0,0,0,0.05);
    transition:
        transform 0.28s cubic-bezier(.4,2,.6,1),
        box-shadow 0.28s,
        border-color 0.25s,
        background 0.3s;
    border: 2px solid #e6f2ff;
    position: relative;
    z-index: 1;
    margin-bottom: 30px;
    padding: 25px;
}
.price-card,
.service-card {
    background: linear-gradient(120deg, #e6f2ff 80%, #fff 100%);
    border-width: 2.5px;
    border-color: #0d6efd;
    padding: 36px 28px 32px 28px;
    margin-bottom: 40px;
    height: 100%;
}
.feature-box {
    padding: 20px;
    margin-bottom: 20px;
    height: 100%;
}
.testimonial-card {
    padding: 25px;
}
.contact-card {
    background-color: #fff;
    padding: 30px;
}
.contact-info-card {
    padding: 25px;
}
.card:hover, .card:focus-within,
.price-card:hover, .price-card:focus-within,
.service-card:hover, .service-card:focus-within,
.contact-card:hover, .contact-card:focus-within,
.feature-box:hover, .feature-box:focus-within,
.testimonial-card:hover, .testimonial-card:focus-within,
.contact-info-card:hover, .contact-info-card:focus-within {
    transform: scale(1.045) translateY(-8px) rotate(-1.2deg);
    box-shadow: 0 12px 32px rgba(13,110,253,0.18), 0 2px 8px rgba(255,193,7,0.10);
    border-color: #ffc107;
    z-index: 2;
    background: linear-gradient(120deg, #d9eafc 70%, #fffbe6 100%);
}

/* ====== POSEBNI KARTIČNI ELEMENTI ====== */
.price-title {
    font-size: 1.6rem;
    font-weight: 700;
    margin-bottom: 25px;
    color: #0d6efd;
    letter-spacing: 0.5px;
}
.price-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    font-size: 1.1rem;
}
.price-value {
    font-weight: bold;
    color: #ff8c00;
    font-size: 1.15rem;
}
.percentage {
    font-weight: bold;
    color: #0d6efd;
    font-size: 1.15rem;
}
.price-feature {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
    font-size: 1.08rem;
}
.price-feature i {
    color: #28a745;
    margin-right: 10px;
    margin-top: 4px;
    font-size: 1.2rem;
}
.service-icon,
.feature-icon,
.contact-icon {
    font-size: 30px;
    color: #0d6efd;
    margin-bottom: 15px;
    transition: color 0.3s;
}
.service-icon {
    font-size: 48px;
    margin-bottom: 22px;
}
.service-card:hover .service-icon {
    color: #ffc107;
}
.rating {
    color: #ffc107;
    margin-bottom: 10px;
}
.logo-large {
    max-width: 350px;
    width: 100%;
    display: block;
    margin-left: auto;
    margin-right: auto;
}

.carousel-inner img {
    max-height: 350px;
    width: 100%;
    object-fit: cover;
    border-radius: 10px;
}

/* ====== DESTINACIJE ====== */
.destination-card {
    margin-bottom: 40px;
    border-bottom: 1px solid #eee;
    padding-bottom: 20px;
}
.destination-card:last-child {
    border-bottom: none;
}
.destination-img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    border-radius: 8px;
    margin-bottom: 20px;
}
.price {
    font-size: 24px;
    font-weight: bold;
    color: #0d6efd;
}
.destination-title {
    color: #333;
    margin-bottom: 15px;
    border-bottom: 2px solid #0d6efd;
    display: inline-block;
    padding-bottom: 5px;
}

/* ====== DODATNI ELEMENTI ====== */
.bg-light-blue {
    background-color: #e6f2ff;
}
.contact-title {
    font-size: 24px;
    margin-bottom: 20px;
    color: #333;
}
.contact-info {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}
.contact-info p {
    margin-bottom: 0;
}
.contact-info .address-line {
    margin-bottom: 5px;
}
.working-hours {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}
.page-title {
    color: #333;
    margin-bottom: 30px;
    padding-bottom: 10px;
    border-bottom: 2px solid #0d6efd;
    display: inline-block;
    text-align: center;
    font-weight: bold;
}
.page-subtitle {
    text-align: center;
    max-width: 600px;
    margin: 0 auto 50px;
    color: #666;
}
.contact-banner {
    background-color: #e6f2ff;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    margin-top: 30px;
    margin-bottom: 30px;
}
footer {
    background-color: #d9eafc;
}

/* ====== DIVIDER ====== */
.fancy-divider {
    width: 100%;
    text-align: center;
    margin: 50px 0 40px 0;
    position: relative;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.fancy-divider-line {
    flex: 1;
    height: 4px;
    border-radius: 2px;
    background: linear-gradient(90deg, #0d6efd 0%, #ffc107 100%);
    opacity: 0.7;
}
.fancy-divider-icon {
    background: #fff;
    padding: 0 18px;
    font-size: 2rem;
    color: #0d6efd;
    z-index: 2;
    margin: 0 10px;
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(13,110,253,0.08);
    display: flex;
    align-items: center;
    justify-content: center;
}

/* ====== FORME ====== */
.form-control {
    border-radius: 5px;
    padding: 12px;
    margin-bottom: 15px;
    border: 2px solid #e6f2ff;
    background: #fff;
    font-size: 1.08rem;
    transition: border-color 0.22s, box-shadow 0.22s, background 0.22s;
}
.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 2px #0d6efd33;
    background: #fafdff;
    outline: none;
}
.form-control:focus + .form-label,
.form-label:focus {
    color: #0d6efd;
    font-weight: 600;
}

/* ====== GUMBI ====== */
.btn-warning.rounded-pill.px-4 {
    transition:
        transform 0.25s cubic-bezier(.4,2,.6,1),
        box-shadow 0.25s,
        background 0.2s,
        color 0.2s;
    box-shadow: 0 2px 8px rgba(13,110,253,0.08);
    font-weight: 600;
    letter-spacing: 0.5px;
}
.btn-warning.rounded-pill.px-4:hover,
.btn-warning.rounded-pill.px-4:focus {
    transform: scale(1.13) translateY(-4px) rotate(-2deg);
    box-shadow: 0 8px 24px rgba(255,193,7,0.25), 0 2px 8px rgba(13,110,253,0.10);
    background: linear-gradient(90deg, #ffc107 80%, #fffbe6 100%);
    color: #222;
    text-decoration: none;
    outline: none;
}
.btn-orange {
    background-color: #ff8c00;
    border: none;
    color: white;
    padding: 12px;
    border-radius: 5px;
    width: 100%;
    font-weight: 500;
    transition:
        background 0.22s,
        color 0.22s,
        box-shadow 0.22s,
        transform 0.18s;
    box-shadow: 0 2px 8px rgba(255,140,0,0.08);
}
.btn-orange:hover, .btn-orange:focus {
    background: linear-gradient(90deg, #ffb300 80%, #fffbe6 100%);
    color: #222;
    box-shadow: 0 8px 24px rgba(255,193,7,0.18);
    transform: scale(1.04) translateY(-2px);
    outline: none;
}
.btn.active-location {
    background: linear-gradient(90deg, #ffc107 80%, #fffbe6 100%) !important;
    color: #222 !important;
    border-color: #ffc107 !important;
    box-shadow: 0 4px 16px rgba(255,193,7,0.18);
    font-weight: 700;
    transform: scale(1.06);
    outline: none;
}
.btn-reserve,
.btn-reserve-outline {
    display: block;
    width: 100%;
    padding: 16px 0;
    font-weight: 700;
    font-size: 1.1rem;
    color: #0d6efd;
    background: transparent;
    border: 2px solid #0d6efd;
    border-radius: 8px;
    transition:
        background 0.2s,
        color 0.2s,
        border-color 0.2s,
        transform 0.2s;
    box-shadow: none;
    outline: none;
}
.btn-reserve:hover,
.btn-reserve-outline:hover,
.price-card:hover .btn-reserve:hover,
.price-card:hover .btn-reserve-outline:hover {
    background: #ff8c00;
    color: #fff;
    border-color: #ff8c00;
    transform: scale(1.04);
    text-decoration: none;
}

/* ====== MAPA ====== */
.map-container {
    width: 100%;
    height: 450px;
    min-height: 300px;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0,0,0,0.05);
}

.bg-light-blue {
            background-color: #e6f2ff;
        }
        .contact-section {
            background-color: #e6f2ff;
            border-radius: 10px;
            padding: 20px;
            margin-top: 40px;
        }

/* ====== PARTNER LOGOVI ====== */
.partner-logos {
    padding: 20px;
}

.partner-link {
    display: block;
    text-decoration: none;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-radius: 8px;
    overflow: hidden;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.partner-link:hover {
    transform: scale(1.05) translateY(-3px);
    box-shadow: 0 8px 20px rgba(13,110,253,0.15);
    text-decoration: none;
}

.partner-img {
    width: 100%;
    height: 80px;
    object-fit: contain;
    padding: 10px;
    background: #fff;
    transition: filter 0.3s ease;
}

.partner-link:hover .partner-img {
    filter: brightness(1.1);
}

/* Responsive adjustments for partner logos */
@media (max-width: 768px) {
    .partner-img {
        height: 60px;
        padding: 8px;
    }

    .partner-logos h4 {
        font-size: 1.2rem;
    }
}

/* ====== STANDARDNE VELIČINE SLIKA ====== */
/* Slike u sekciji mjesečnih izleta */
.card-img-top {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 8px 8px 0 0;
}

/* Slike u sekciji odredišta */
.destination-section .card-img-top {
    width: 100%;
    height: 220px;
    object-fit: cover;
    border-radius: 8px 8px 0 0;
}

/* Responsive adjustments for card images */
@media (max-width: 768px) {
    .card-img-top {
        height: 180px;
    }

    .destination-section .card-img-top {
        height: 200px;
    }
}