<!DOCTYPE html>
<html lang="hr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prije<PERSON>z putnika - MarijanBus</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Open+Sans:wght@400;500&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Navigacija -->
    <nav class="navbar navbar-expand-lg navbar-light" style="background-color: #d9eafc;">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.html">Početna stranica</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                    aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item"><a class="nav-link" href="o-nama.html">O nama</a></li>
                    <li class="nav-item"><a class="nav-link text-danger fw-bold" href="usluge.html">Usluge</a></li>
                    <li class="nav-item"><a class="nav-link" href="cjenik.html">Cjenik</a></li>
                    <li class="nav-item"><a class="nav-link" href="turisticki-prijevoz.html">Turistički prijevoz</a></li>
                    <li class="nav-item"><a class="nav-link" href="kontakt.html">Kontakt</a></li>
                </ul>
            </div>
            <div class="d-none d-lg-flex">
                <a href="mailto:<EMAIL>" class="social-icon"><i class="fas fa-envelope"></i></a>
                <a href="tel:+38512345678" class="social-icon"><i class="fas fa-phone"></i></a>
                <a href="https://facebook.com/marijanbus" class="social-icon"><i class="fab fa-facebook-f"></i></a>
            </div>
        </div>
    </nav>

    <!-- Glavni sadržaj -->
    <div class="container mt-5">
        <h1 class="text-center mb-5">Osobna Putovanja</h1>

        <div class="row align-items-center mb-5">
            <div class="col-lg-6">
                <h2 class="mb-4">Profesionalan i Pouzdan Prijevoz</h2>
                <p>Nudimo vrhunsku uslugu osobnog prijevoza prilagođenu Vašim potrebama i željama. Bilo to na jednodnevne izlete i događaje ili na višednevna putovanja i izlete.</p>

                <ul class="list-unstyled mt-4">
                    <li class="mb-3"><i class="fas fa-check text-success me-2"></i> 24/7 dostupnost</li>
                    <li class="mb-3"><i class="fas fa-check text-success me-2"></i> Profesionalni vozači</li>
                    <li class="mb-3"><i class="fas fa-check text-success me-2"></i> Premium vozni park</li>
                </ul>

                <a href="#" class="btn btn-warning rounded-pill px-4 py-2 mt-3 open-rezervacija-modal">Rezerviraj Odmah</a>
            </div>
            <div class="col-lg-6">
                <img src="resursi/osobno.png" alt="Obitelj u autobusu" class="img-fluid rounded-4">
            </div>
        </div>
        <div class="fancy-divider">
            <div class="fancy-divider-line"></div>
            <span class="fancy-divider-icon">
                <i class="fas fa-bus"></i>
            </span>
            <div class="fancy-divider-line"></div>
        </div>
        <div class="service-card mb-4 border-0 rounded-4 shadow-sm">
            <div class="card-body">
                <h2 class="card-title mb-4">Osobni Prijevoz</h2>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <p class="fw-bold">Predujam:</p>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <p class="text-primary fw-bold">20%</p>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-md-6">
                        <p class="fw-bold">Početna cijena od:</p>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <p class="text-warning fw-bold">100€</p>
                    </div>
                </div>

                <ul class="list-unstyled">
                    <li class="mb-3"><i class="fas fa-check text-success me-2"></i> Obiteljski izleti</li>
                    <li class="mb-3"><i class="fas fa-check text-success me-2"></i> Vjenčanja</li>
                    <li class="mb-3"><i class="fas fa-check text-success me-2"></i> Proslave</li>
                    <li class="mb-3"><i class="fas fa-check text-success me-2"></i> Udobna vozila</li>
                    <li class="mb-3"><i class="fas fa-check text-success me-2"></i> Fleksibilno vrijeme</li>
                </ul>

                <a href="#" class="btn btn-primary w-100 rounded-pill py-2 mt-3 open-rezervacija-modal">Rezerviraj Prijevoz</a>
            </div>
        </div>

        <div class="contact-section text-center py-4" id="kontakt">
            <h3 class="mb-3">Trebate prilagođenu ponudu? Kontaktirajte nas za detaljni izračun cijene prema vašim potrebama.</h3>
            <p class="mb-3">
                <strong>Tel: <a href="tel:+385012345678" class="text-primary">+385 (0)1 234 5678</a> | Email: <a href="mailto:<EMAIL>" class="text-primary"><EMAIL></a></strong>
            </p>
        </div>
    </div>

    <!-- Modal za rezervaciju -->
    <div class="modal fade" id="rezervacijaModal" tabindex="-1" aria-labelledby="rezervacijaModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content p-3">
                <div class="modal-header border-0">
                    <h4 class="modal-title fw-bold" id="rezervacijaModalLabel">Rezervacija mjesta</h4>
                    <button type="button" class="btn btn-danger rounded-pill px-4 py-1 ms-auto" data-bs-dismiss="modal" id="odustaniBtn">Odustani</button>
                </div>
                <div class="modal-body pt-0">
                    <form id="rezervacijaForm" novalidate>
                        <div class="mb-3">
                            <label class="form-label">Polazište:</label>
                            <input type="text" class="form-control" name="polaziste" required>
                            <div class="invalid-feedback">Unesite polazište.</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Odredište:</label>
                            <input type="text" class="form-control" name="odrediste" required>
                            <div class="invalid-feedback">Unesite odredište.</div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Datum polaska:</label>
                                <input type="date" class="form-control" name="datumPolaska" required>
                                <div class="invalid-feedback">Odaberite datum polaska.</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Vrijeme polaska:</label>
                                <input type="time" class="form-control" name="vrijemePolaska" required>
                                <div class="invalid-feedback">Odaberite vrijeme polaska.</div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Broj putnika:</label>
                            <input type="number" class="form-control" name="brojPutnika" min="1" required>
                            <div class="invalid-feedback">Unesite broj putnika (najmanje 1).</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Način plaćanja:</label>
                            <select class="form-control" name="nacinPlacanja" required>
                                <option value="">Odaberite način plaćanja</option>
                                <option>Kreditna kartica</option>
                                <option>Gotovina</option>
                                <option>Transakcijski račun</option>
                            </select>
                            <div class="invalid-feedback">Odaberite način plaćanja.</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">E-mail:</label>
                            <input type="email" class="form-control" name="email" required>
                            <div class="invalid-feedback">Unesite ispravan e-mail.</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Telefon:</label>
                            <input type="tel" class="form-control" name="telefon" required>
                            <div class="invalid-feedback">Unesite broj telefona.</div>
                        </div>
                        <button type="submit" class="btn btn-orange mt-2 w-100">Rezerviraj</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal za uspješno slanje -->
    <div class="modal fade" id="successModal" tabindex="-1" aria-labelledby="successModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content" style="border-radius: 32px;">
                <div class="modal-body text-center py-5">
                    <h2 class="fw-bold mb-3" style="font-size:2.2rem;">Poruka uspješno poslana</h2>
                    <p class="mb-4 text-secondary" style="font-size:1.1rem;">Javit ćemo vam se čim je prije moguće</p>
                    <button type="button" class="btn btn-dark btn-lg px-5 rounded-pill" data-bs-dismiss="modal">U redu</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="py-4" style="background-color: #d9eafc;">
        <div class="container">
            <div class="row">
                <div class="col-md-3 mb-4 mb-md-0">
                    <img src="resursi/Logo.png" alt="Marijan Bus Logo" class="img-fluid" style="max-width: 150px;">
                </div>
                <div class="col-md-3 mb-4 mb-md-0">
                    <h5 class="mb-3">Saznajte više</h5>
                    <ul class="list-unstyled">
                        <li><a href="o-nama.html" class="text-decoration-none text-dark">O nama</a></li>
                        <li><a href="usluge.html" class="text-decoration-none text-dark">Usluge</a></li>
                        <li><a href="cjenik.html" class="text-decoration-none text-dark">Cjenik</a></li>
                        <li><a href="kontakt.html" class="text-decoration-none text-dark">Kontakt</a></li>
                    </ul>
                </div>
                <div class="col-md-3 mb-4 mb-md-0">
                    <h5 class="mb-3">Radno vrijeme</h5>
                    <div class="d-flex justify-content-between mb-1">
                        <span>Pon -Pet: 08:00 - 16:00</span>
                    </div>
                    <div class="d-flex justify-content-between mb-1">
                        <span>Sub: 09:00 - 13:00</span>
                    </div>
                    <div class="d-flex justify-content-between mb-1">
                        <span>Ned: Zatvoreno</span>
                    </div>
                </div>
                <div class="col-md-3">
                    <h5 class="mb-3">Kontaktiraj nas</h5>
                    <p class="mb-1">Telefon: <a href="tel:+38512345678" class="text-decoration-none text-dark">+385 1 234 5678</a></p>
                    <p>Email: <a href="mailto:<EMAIL>" class="text-decoration-none text-dark"><EMAIL></a></p>
                    <h5 class="mt-3 mb-2">Društvene mreže</h5>
                    <div class="d-flex">
                        <a href="#" class="me-2 text-dark"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="me-2 text-dark"><i class="fab fa-youtube"></i></a>
                        <a href="#" class="text-dark"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12 text-center">
                    <small>© 2025 MarijanBus. All rights reserved.</small>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" crossorigin="anonymous"></script>
    <script>
    document.addEventListener('DOMContentLoaded', function () {
        // Postavi minimalni datum za polazak
        const today = new Date().toISOString().split("T")[0];
        const datumPolaska = document.querySelector('input[name="datumPolaska"]');
        if (datumPolaska) datumPolaska.min = today;

        // Otvaranje modala na oba gumba
        document.querySelectorAll('.open-rezervacija-modal').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                const modal = new bootstrap.Modal(document.getElementById('rezervacijaModal'));
                modal.show();
            });
        });

        // Validacija forme
        const form = document.getElementById('rezervacijaForm');
        form.addEventListener('submit', function (e) {
            e.preventDefault();
            if (!form.checkValidity()) {
                form.classList.add('was-validated');
                return;
            }
            // Zatvori modal za rezervaciju
            const rezervacijaModal = bootstrap.Modal.getInstance(document.getElementById('rezervacijaModal'));
            if (rezervacijaModal) rezervacijaModal.hide();

            // Prikaži modal za uspjeh
            setTimeout(function() {
                const modal = new bootstrap.Modal(document.getElementById('successModal'));
                modal.show();
            }, 400);

            form.reset();
            form.classList.remove('was-validated');
        });

        // Gumb Odustani - zatvara modal
        document.getElementById('odustaniBtn').addEventListener('click', function() {
            const rezervacijaModal = bootstrap.Modal.getInstance(document.getElementById('rezervacijaModal'));
            if (rezervacijaModal) rezervacijaModal.hide();
        });
    });
    </script>
</body>
</html>

